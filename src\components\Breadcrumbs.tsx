'use client'

import { ChevronRight, Home } from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface BreadcrumbItem {
  id: string | null
  name: string
}

interface BreadcrumbsProps {
  breadcrumbs: BreadcrumbItem[]
  onNavigate: (folderId: string | null) => void
}

export function Breadcrumbs({ breadcrumbs, onNavigate }: BreadcrumbsProps) {
  return (
    <nav className="flex items-center space-x-1 text-sm text-gray-600 bg-white p-3 rounded-lg border">
      {breadcrumbs.map((item, index) => (
        <div key={item.id || 'root'} className="flex items-center">
          {index > 0 && (
            <ChevronRight className="h-4 w-4 mx-1 text-gray-400" />
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onNavigate(item.id)}
            className={`px-2 py-1 h-auto font-normal ${
              index === breadcrumbs.length - 1
                ? 'text-gray-900 font-medium cursor-default'
                : 'text-blue-600 hover:text-blue-800 hover:bg-blue-50'
            }`}
            disabled={index === breadcrumbs.length - 1}
          >
            {index === 0 ? (
              <div className="flex items-center">
                <Home className="h-4 w-4 mr-1" />
                {item.name}
              </div>
            ) : (
              item.name
            )}
          </Button>
        </div>
      ))}
    </nav>
  )
}
