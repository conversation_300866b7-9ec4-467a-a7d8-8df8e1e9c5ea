import { deriveM<PERSON><PERSON><PERSON>, generateSalt, encryptFile<PERSON>ey, decrypt<PERSON>ile<PERSON><PERSON> } from './encryption'

// Key management for user's encryption keys
export class KeyManager {
  private masterKey: string | null = null
  private userSalt: string | null = null

  // Initialize key manager with user's password
  async initializeWithPassword(password: string, salt?: string): Promise<void> {
    if (!salt) {
      // Generate new salt for new user
      this.userSalt = generateSalt()
    } else {
      this.userSalt = salt
    }
    
    this.masterKey = deriveMasterKey(password, this.userSalt)
  }

  // Get user's salt (needed for login)
  getUserSalt(): string | null {
    return this.userSalt
  }

  // Set user salt (when logging in existing user)
  setUserSalt(salt: string): void {
    this.userSalt = salt
  }

  // Check if key manager is initialized
  isInitialized(): boolean {
    return this.masterKey !== null && this.userSalt !== null
  }

  // Encrypt a file key with the user's master key
  encryptFileKey(fileKey: string): string {
    if (!this.masterKey) {
      throw new Error('Key manager not initialized')
    }
    return encryptFile<PERSON>ey(fileKey, this.masterKey)
  }

  // Decrypt a file key with the user's master key
  decryptFileKey(encryptedFileKey: string): string {
    if (!this.masterKey) {
      throw new Error('Key manager not initialized')
    }
    return decryptFileKey(encryptedFileKey, this.masterKey)
  }

  // Clear all keys from memory (logout)
  clear(): void {
    this.masterKey = null
    this.userSalt = null
  }

  // Change user password (re-encrypt all file keys)
  async changePassword(
    oldPassword: string, 
    newPassword: string,
    encryptedFileKeys: string[]
  ): Promise<string[]> {
    if (!this.userSalt) {
      throw new Error('User salt not available')
    }

    // Verify old password
    const oldMasterKey = deriveMasterKey(oldPassword, this.userSalt)
    
    // Generate new salt and master key
    const newSalt = generateSalt()
    const newMasterKey = deriveMasterKey(newPassword, newSalt)

    // Re-encrypt all file keys
    const reencryptedKeys: string[] = []
    
    for (const encryptedKey of encryptedFileKeys) {
      try {
        // Decrypt with old key
        const fileKey = decryptFileKey(encryptedKey, oldMasterKey)
        // Re-encrypt with new key
        const newEncryptedKey = encryptFileKey(fileKey, newMasterKey)
        reencryptedKeys.push(newEncryptedKey)
      } catch (error) {
        throw new Error('Failed to re-encrypt file key - invalid old password')
      }
    }

    // Update internal state
    this.userSalt = newSalt
    this.masterKey = newMasterKey

    return reencryptedKeys
  }
}

// Global key manager instance
export const keyManager = new KeyManager()

// Storage for encrypted keys in session storage (cleared on browser close)
export class SessionKeyStorage {
  private static readonly STORAGE_KEY = 'encrypted_session_keys'

  // Store encrypted file keys temporarily
  static storeKeys(keys: Record<string, string>): void {
    try {
      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(keys))
    } catch (error) {
      console.warn('Failed to store keys in session storage:', error)
    }
  }

  // Retrieve encrypted file keys
  static getKeys(): Record<string, string> {
    try {
      const stored = sessionStorage.getItem(this.STORAGE_KEY)
      return stored ? JSON.parse(stored) : {}
    } catch (error) {
      console.warn('Failed to retrieve keys from session storage:', error)
      return {}
    }
  }

  // Store a single file key
  static storeKey(fileId: string, encryptedKey: string): void {
    const keys = this.getKeys()
    keys[fileId] = encryptedKey
    this.storeKeys(keys)
  }

  // Get a single file key
  static getKey(fileId: string): string | null {
    const keys = this.getKeys()
    return keys[fileId] || null
  }

  // Remove a file key
  static removeKey(fileId: string): void {
    const keys = this.getKeys()
    delete keys[fileId]
    this.storeKeys(keys)
  }

  // Clear all stored keys
  static clear(): void {
    try {
      sessionStorage.removeItem(this.STORAGE_KEY)
    } catch (error) {
      console.warn('Failed to clear session storage:', error)
    }
  }
}

// Utility functions for key validation
export function validatePassword(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}
