import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const parentId = searchParams.get('parentId') || null
    const path = searchParams.get('path') || '/'
    const search = searchParams.get('search')
    const type = searchParams.get('type')
    const sortBy = searchParams.get('sortBy') || 'name'
    const sortOrder = searchParams.get('sortOrder') || 'asc'
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Create Supabase client with cookies
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options)
            })
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Build query
    let query = supabase
      .from('files')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id)

    // Filter by parent folder
    if (parentId) {
      query = query.eq('parent_id', parentId)
    } else {
      query = query.is('parent_id', null)
    }

    // Search filter
    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    // Type filter
    if (type) {
      if (type === 'folder') {
        query = query.eq('is_folder', true)
      } else if (type === 'file') {
        query = query.eq('is_folder', false)
      } else {
        query = query.ilike('type', `${type}%`)
      }
    }

    // Sorting
    const validSortFields = ['name', 'size', 'created_at', 'updated_at', 'type']
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'name'
    const ascending = sortOrder === 'asc'

    query = query.order(sortField, { ascending })

    // Pagination
    query = query.range(offset, offset + limit - 1)

    const { data: files, error: filesError, count } = await query

    if (filesError) {
      console.error('Files query error:', filesError)
      return NextResponse.json({ error: 'Failed to fetch files' }, { status: 500 })
    }

    // Get breadcrumb path if parentId is provided
    let breadcrumbs: any[] = []
    if (parentId) {
      const { data: parentFile } = await supabase
        .from('files')
        .select('id, name, parent_id')
        .eq('id', parentId)
        .eq('user_id', user.id)
        .single()

      if (parentFile) {
        // Build breadcrumb trail
        let currentId = parentFile.parent_id
        breadcrumbs.unshift({ id: parentFile.id, name: parentFile.name })

        while (currentId) {
          const { data: parent } = await supabase
            .from('files')
            .select('id, name, parent_id')
            .eq('id', currentId)
            .eq('user_id', user.id)
            .single()

          if (parent) {
            breadcrumbs.unshift({ id: parent.id, name: parent.name })
            currentId = parent.parent_id
          } else {
            break
          }
        }
      }
    }

    // Add root to breadcrumbs
    breadcrumbs.unshift({ id: null, name: 'Home' })

    return NextResponse.json({
      success: true,
      data: {
        files: files || [],
        breadcrumbs,
        pagination: {
          total: count || 0,
          limit,
          offset,
          hasMore: (count || 0) > offset + limit
        }
      }
    })

  } catch (error) {
    console.error('Files listing error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Create folder
export async function POST(request: NextRequest) {
  try {
    const { name, parentId, path } = await request.json()

    if (!name || !path) {
      return NextResponse.json({ error: 'Name and path are required' }, { status: 400 })
    }

    // Create Supabase client with cookies
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options)
            })
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if folder already exists
    const { data: existingFolder } = await supabase
      .from('files')
      .select('id')
      .eq('user_id', user.id)
      .eq('path', path)
      .eq('is_folder', true)
      .single()

    if (existingFolder) {
      return NextResponse.json({ error: 'Folder already exists' }, { status: 409 })
    }

    // Validate parent folder if provided
    if (parentId) {
      const { data: parentFolder, error: parentError } = await supabase
        .from('files')
        .select('id, is_folder')
        .eq('id', parentId)
        .eq('user_id', user.id)
        .single()

      if (parentError || !parentFolder || !parentFolder.is_folder) {
        return NextResponse.json({ error: 'Invalid parent folder' }, { status: 400 })
      }
    }

    // Create folder record
    const { data: folder, error: folderError } = await supabase
      .from('files')
      .insert({
        name,
        size: 0,
        type: 'folder',
        path,
        parent_id: parentId,
        user_id: user.id,
        encrypted_key: '', // Folders don't need encryption keys
        checksum: '',
        is_folder: true,
        version: 1
      })
      .select()
      .single()

    if (folderError) {
      console.error('Folder creation error:', folderError)
      return NextResponse.json({ error: 'Failed to create folder' }, { status: 500 })
    }

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: user.id,
        action: 'folder_create',
        file_id: folder.id,
        details: {
          folder_name: name,
          path: path,
          parent_id: parentId
        },
        ip_address: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown'
      })

    return NextResponse.json({
      success: true,
      folder
    })

  } catch (error) {
    console.error('Folder creation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
