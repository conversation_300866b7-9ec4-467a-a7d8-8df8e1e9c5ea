{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_574bffa5._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_6e7324c0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KzrtSYEFbLXUtA9qFJrfNR2JasCj+WoLCCfWcRC4PlU=", "__NEXT_PREVIEW_MODE_ID": "9603543c6eff4e6e8e701f250c54c722", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c9c7a546a3ae35f7773a75847b176b4e22543711b5e612273b97a0ebf4f93cbe", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ed8a285ce5e6f9342dbbd494aea1c38aa7b33e892d605ad83109fab4fe848e22"}}}, "instrumentation": null, "functions": {}}