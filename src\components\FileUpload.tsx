'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Progress } from '@/components/ui/Progress'
import { FileEncryptionService } from '@/lib/fileEncryption'
import { isAllowedFileType, isValidFileSize, formatFileSize } from '@/lib/utils'
import { Upload, X, CheckCircle, AlertCircle, File } from 'lucide-react'
import toast from 'react-hot-toast'

interface FileUploadProps {
  currentFolderId: string | null
  currentPath: string
  onClose: () => void
  onSuccess: () => void
}

interface UploadFile {
  file: File
  id: string
  status: 'pending' | 'encrypting' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
}

export function FileUpload({ currentFolderId, currentPath, onClose, onSuccess }: FileUploadProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      file,
      id: crypto.randomUUID(),
      status: 'pending',
      progress: 0
    }))

    // Validate files
    const validFiles = newFiles.filter(({ file }) => {
      if (!isAllowedFileType(file)) {
        toast.error(`File type not allowed: ${file.name}`)
        return false
      }
      if (!isValidFileSize(file)) {
        toast.error(`File too large: ${file.name}`)
        return false
      }
      return true
    })

    setUploadFiles(prev => [...prev, ...validFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    disabled: isUploading
  })

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== id))
  }

  const updateFileStatus = (id: string, updates: Partial<UploadFile>) => {
    setUploadFiles(prev => prev.map(f => 
      f.id === id ? { ...f, ...updates } : f
    ))
  }

  const uploadFile = async (uploadFile: UploadFile) => {
    const { file, id } = uploadFile

    try {
      // Encryption phase
      updateFileStatus(id, { status: 'encrypting', progress: 0 })
      
      const encryptedData = await FileEncryptionService.encryptFileForUpload(file)
      
      updateFileStatus(id, { status: 'uploading', progress: 0 })

      // Create form data
      const formData = new FormData()
      formData.append('file', new Blob([encryptedData.encryptedData]), file.name)
      formData.append('encryptedKey', encryptedData.encryptedKey)
      formData.append('checksum', encryptedData.checksum)
      formData.append('parentId', currentFolderId || '')
      
      const filePath = currentPath 
        ? `/${currentPath}/${file.name}`
        : `/${file.name}`
      formData.append('path', filePath)

      // Upload with progress tracking
      const xhr = new XMLHttpRequest()
      
      return new Promise<void>((resolve, reject) => {
        xhr.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            const progress = Math.round((e.loaded / e.total) * 100)
            updateFileStatus(id, { progress })
          }
        })

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            const result = JSON.parse(xhr.responseText)
            if (result.success) {
              updateFileStatus(id, { status: 'success', progress: 100 })
              resolve()
            } else {
              updateFileStatus(id, { 
                status: 'error', 
                error: result.error || 'Upload failed' 
              })
              reject(new Error(result.error))
            }
          } else {
            updateFileStatus(id, { 
              status: 'error', 
              error: `HTTP ${xhr.status}: ${xhr.statusText}` 
            })
            reject(new Error(`HTTP ${xhr.status}`))
          }
        })

        xhr.addEventListener('error', () => {
          updateFileStatus(id, { 
            status: 'error', 
            error: 'Network error' 
          })
          reject(new Error('Network error'))
        })

        xhr.open('POST', '/api/files/upload')
        xhr.send(formData)
      })

    } catch (error) {
      console.error('Upload error:', error)
      updateFileStatus(id, { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Upload failed' 
      })
      throw error
    }
  }

  const startUpload = async () => {
    if (uploadFiles.length === 0) return

    setIsUploading(true)
    let successCount = 0
    let errorCount = 0

    try {
      // Upload files sequentially to avoid overwhelming the server
      for (const fileToUpload of uploadFiles) {
        if (fileToUpload.status === 'pending') {
          try {
            await uploadFile(fileToUpload)
            successCount++
          } catch (error) {
            errorCount++
          }
        }
      }

      if (successCount > 0) {
        toast.success(`${successCount} file(s) uploaded successfully`)
        if (errorCount === 0) {
          onSuccess()
        }
      }

      if (errorCount > 0) {
        toast.error(`${errorCount} file(s) failed to upload`)
      }

    } finally {
      setIsUploading(false)
    }
  }

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <File className="h-5 w-5 text-gray-500" />
    }
  }

  const canUpload = uploadFiles.length > 0 && !isUploading && 
    uploadFiles.some(f => f.status === 'pending')

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[80vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Upload Files</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Drop zone */}
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            {isDragActive ? (
              <p className="text-blue-600">Drop the files here...</p>
            ) : (
              <div>
                <p className="text-gray-600 mb-2">
                  Drag & drop files here, or click to select files
                </p>
                <p className="text-sm text-gray-500">
                  Maximum file size: {formatFileSize(parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '104857600'))}
                </p>
              </div>
            )}
          </div>

          {/* File list */}
          {uploadFiles.length > 0 && (
            <div className="max-h-60 overflow-y-auto space-y-2">
              {uploadFiles.map((uploadFile) => (
                <div key={uploadFile.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  {getStatusIcon(uploadFile.status)}
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {uploadFile.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(uploadFile.file.size)}
                    </p>
                    
                    {uploadFile.status === 'encrypting' && (
                      <p className="text-xs text-blue-600">Encrypting...</p>
                    )}
                    
                    {uploadFile.status === 'uploading' && (
                      <div className="mt-1">
                        <Progress value={uploadFile.progress} className="h-1" />
                        <p className="text-xs text-blue-600 mt-1">
                          Uploading... {uploadFile.progress}%
                        </p>
                      </div>
                    )}
                    
                    {uploadFile.status === 'error' && (
                      <p className="text-xs text-red-600 mt-1">
                        {uploadFile.error}
                      </p>
                    )}
                  </div>

                  {uploadFile.status === 'pending' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(uploadFile.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={startUpload} 
              disabled={!canUpload}
            >
              {isUploading ? 'Uploading...' : `Upload ${uploadFiles.filter(f => f.status === 'pending').length} file(s)`}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
