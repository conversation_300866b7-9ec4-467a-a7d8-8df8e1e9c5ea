'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { keyManager } from '@/lib/keyManager'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Alert, AlertDescription } from '@/components/ui/Alert'
import { FileExplorer } from '@/components/FileExplorer'
import { 
  Shield, 
  Lock, 
  Key, 
  Eye, 
  EyeOff,
  AlertTriangle 
} from 'lucide-react'
import toast from 'react-hot-toast'

export default function DashboardPage() {
  const { user, profile, loading } = useAuth()
  const router = useRouter()
  const [masterPassword, setMasterPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isUnlocking, setIsUnlocking] = useState(false)
  const [isUnlocked, setIsUnlocked] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    // Check if key manager is already initialized
    if (keyManager.isInitialized()) {
      setIsUnlocked(true)
    }
  }, [])

  const handleUnlock = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsUnlocking(true)
    setError('')

    try {
      if (!profile?.salt) {
        throw new Error('User profile not found or missing encryption salt')
      }

      // Initialize key manager with user's password and salt
      await keyManager.initializeWithPassword(masterPassword, profile.salt)
      
      setIsUnlocked(true)
      setMasterPassword('')
      toast.success('Files unlocked successfully')
    } catch (err) {
      console.error('Unlock error:', err)
      setError('Failed to unlock files. Please check your password.')
      toast.error('Failed to unlock files')
    } finally {
      setIsUnlocking(false)
    }
  }

  const handleLock = () => {
    keyManager.clear()
    setIsUnlocked(false)
    setMasterPassword('')
    toast.success('Files locked successfully')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">Secure File Explorer</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${isUnlocked ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm text-gray-600">
                  {isUnlocked ? 'Unlocked' : 'Locked'}
                </span>
              </div>
              {isUnlocked && (
                <Button variant="outline" size="sm" onClick={handleLock}>
                  <Lock className="h-4 w-4 mr-2" />
                  Lock Files
                </Button>
              )}
              <div className="text-sm text-gray-600">
                Welcome, {profile?.full_name || user.email}
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!isUnlocked ? (
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader className="text-center">
                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Key className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>Unlock Your Files</CardTitle>
                <CardDescription>
                  Enter your master password to decrypt and access your files
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleUnlock} className="space-y-4">
                  {error && (
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div>
                    <label htmlFor="masterPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      Master Password
                    </label>
                    <div className="relative">
                      <Input
                        id="masterPassword"
                        type={showPassword ? 'text' : 'password'}
                        value={masterPassword}
                        onChange={(e) => setMasterPassword(e.target.value)}
                        placeholder="Enter your master password"
                        className="pr-10"
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isUnlocking || !masterPassword}
                  >
                    {isUnlocking ? 'Unlocking...' : 'Unlock Files'}
                  </Button>
                </form>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-start">
                    <Shield className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium mb-1">Security Notice</p>
                      <p>
                        Your master password is used to decrypt your files locally. 
                        We never store or transmit your password or decryption keys.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <FileExplorer />
        )}
      </main>
    </div>
  )
}
