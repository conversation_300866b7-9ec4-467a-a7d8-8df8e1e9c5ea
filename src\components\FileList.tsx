'use client'

import { useState } from 'react'
import { FileRecord } from '@/lib/supabase'
import { formatFileSize, formatDate, getFileIcon } from '@/lib/utils'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { 
  ChevronUp, 
  ChevronDown, 
  Download, 
  Share, 
  Trash2, 
  MoreHorizontal,
  Folder,
  File,
  Image,
  FileText,
  Video,
  Music,
  Archive,
  Code
} from 'lucide-react'
import toast from 'react-hot-toast'

interface FileListProps {
  files: FileRecord[]
  loading: boolean
  viewMode: 'grid' | 'list'
  sortBy: string
  sortOrder: 'asc' | 'desc'
  selectedFiles: Set<string>
  onSort: (field: string) => void
  onNavigate: (folderId: string | null) => void
  onSelect: (fileId: string) => void
  onRefresh: () => void
}

const iconMap = {
  folder: Folder,
  file: File,
  image: Image,
  document: FileText,
  video: Video,
  audio: Music,
  archive: Archive,
  code: Code
}

export function FileList({
  files,
  loading,
  viewMode,
  sortBy,
  sortOrder,
  selectedFiles,
  onSort,
  onNavigate,
  onSelect,
  onRefresh
}: FileListProps) {
  const [contextMenu, setContextMenu] = useState<{
    x: number
    y: number
    fileId: string
  } | null>(null)

  const handleDownload = async (file: FileRecord) => {
    if (file.is_folder) return

    try {
      toast.loading('Downloading file...', { id: 'download' })
      
      const response = await fetch(`/api/files/download?fileId=${file.id}`)
      const result = await response.json()

      if (result.success) {
        // Import encryption functions dynamically to avoid SSR issues
        const { FileEncryptionService } = await import('@/lib/fileEncryption')
        
        // Convert array back to ArrayBuffer
        const encryptedData = new Uint8Array(result.data.encryptedData).buffer
        
        // Decrypt the file
        const decryptionResult = await FileEncryptionService.decryptFileAfterDownload(
          encryptedData,
          result.data.encryptedKey,
          result.data.checksum
        )

        if (!decryptionResult.isValid) {
          toast.error('File integrity check failed', { id: 'download' })
          return
        }

        // Create and download the file
        const blob = FileEncryptionService.createDownloadableBlob(
          decryptionResult.decryptedData,
          result.data.type
        )
        
        FileEncryptionService.downloadFile(blob, result.data.filename)
        toast.success('File downloaded successfully', { id: 'download' })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('Download failed:', error)
      toast.error('Failed to download file', { id: 'download' })
    }
  }

  const handleContextMenu = (e: React.MouseEvent, fileId: string) => {
    e.preventDefault()
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      fileId
    })
  }

  const closeContextMenu = () => {
    setContextMenu(null)
  }

  const handleFileClick = (file: FileRecord) => {
    if (file.is_folder) {
      onNavigate(file.id)
    } else {
      // For files, we could implement preview or download
      handleDownload(file)
    }
  }

  const SortButton = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => onSort(field)}
      className="h-auto p-1 font-medium text-left justify-start"
    >
      {children}
      {sortBy === field && (
        sortOrder === 'asc' ? <ChevronUp className="h-4 w-4 ml-1" /> : <ChevronDown className="h-4 w-4 ml-1" />
      )}
    </Button>
  )

  const FileIcon = ({ file }: { file: FileRecord }) => {
    const iconType = getFileIcon(file.name, file.is_folder)
    const IconComponent = iconMap[iconType as keyof typeof iconMap] || File
    
    return (
      <IconComponent 
        className={`h-5 w-5 ${
          file.is_folder ? 'text-blue-500' : 'text-gray-500'
        }`} 
      />
    )
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading files...</p>
        </CardContent>
      </Card>
    )
  }

  if (files.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Folder className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No files found</p>
          <p className="text-sm text-gray-500 mt-1">Upload some files to get started</p>
        </CardContent>
      </Card>
    )
  }

  if (viewMode === 'grid') {
    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {files.map((file) => (
          <Card
            key={file.id}
            className={`cursor-pointer hover:shadow-md transition-shadow ${
              selectedFiles.has(file.id) ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => handleFileClick(file)}
            onContextMenu={(e) => handleContextMenu(e, file.id)}
          >
            <CardContent className="p-4 text-center">
              <div className="mb-2">
                <FileIcon file={file} />
              </div>
              <p className="text-sm font-medium truncate" title={file.name}>
                {file.name}
              </p>
              {!file.is_folder && (
                <p className="text-xs text-gray-500 mt-1">
                  {formatFileSize(file.size)}
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="text-left p-3 w-8">
                  <input
                    type="checkbox"
                    checked={files.length > 0 && files.every(f => selectedFiles.has(f.id))}
                    onChange={(e) => {
                      if (e.target.checked) {
                        files.forEach(f => onSelect(f.id))
                      } else {
                        files.forEach(f => selectedFiles.has(f.id) && onSelect(f.id))
                      }
                    }}
                  />
                </th>
                <th className="text-left p-3">
                  <SortButton field="name">Name</SortButton>
                </th>
                <th className="text-left p-3">
                  <SortButton field="size">Size</SortButton>
                </th>
                <th className="text-left p-3">
                  <SortButton field="updated_at">Modified</SortButton>
                </th>
                <th className="text-left p-3 w-16">Actions</th>
              </tr>
            </thead>
            <tbody>
              {files.map((file) => (
                <tr
                  key={file.id}
                  className={`border-b hover:bg-gray-50 cursor-pointer ${
                    selectedFiles.has(file.id) ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => handleFileClick(file)}
                  onContextMenu={(e) => handleContextMenu(e, file.id)}
                >
                  <td className="p-3">
                    <input
                      type="checkbox"
                      checked={selectedFiles.has(file.id)}
                      onChange={(e) => {
                        e.stopPropagation()
                        onSelect(file.id)
                      }}
                    />
                  </td>
                  <td className="p-3">
                    <div className="flex items-center">
                      <FileIcon file={file} />
                      <span className="ml-2 truncate">{file.name}</span>
                    </div>
                  </td>
                  <td className="p-3 text-gray-600">
                    {file.is_folder ? '-' : formatFileSize(file.size)}
                  </td>
                  <td className="p-3 text-gray-600">
                    {formatDate(file.updated_at)}
                  </td>
                  <td className="p-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleContextMenu(e, file.id)
                      }}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>

      {/* Context Menu */}
      {contextMenu && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={closeContextMenu}
          />
          <div
            className="fixed z-50 bg-white border rounded-lg shadow-lg py-1 min-w-[150px]"
            style={{
              left: contextMenu.x,
              top: contextMenu.y
            }}
          >
            <button
              className="w-full text-left px-3 py-2 hover:bg-gray-100 flex items-center"
              onClick={() => {
                const file = files.find(f => f.id === contextMenu.fileId)
                if (file && !file.is_folder) {
                  handleDownload(file)
                }
                closeContextMenu()
              }}
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </button>
            <button
              className="w-full text-left px-3 py-2 hover:bg-gray-100 flex items-center"
              onClick={() => {
                // TODO: Implement sharing
                toast('Sharing feature coming soon')
                closeContextMenu()
              }}
            >
              <Share className="h-4 w-4 mr-2" />
              Share
            </button>
            <button
              className="w-full text-left px-3 py-2 hover:bg-gray-100 flex items-center text-red-600"
              onClick={() => {
                // TODO: Implement delete
                toast('Delete feature coming soon')
                closeContextMenu()
              }}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </button>
          </div>
        </>
      )}
    </Card>
  )
}
