import { encryptFile, decryptFile, generateFile<PERSON>ey, calculateChecksum } from './encryption'
import { key<PERSON>anager, SessionKeyStorage } from './keyManager'

export interface EncryptedFileData {
  encryptedData: ArrayBuffer
  encryptedKey: string
  checksum: string
  originalSize: number
  encryptedSize: number
}

export interface FileDecryptionResult {
  decryptedData: ArrayBuffer
  isValid: boolean
  originalChecksum: string
}

// Service for handling file encryption/decryption operations
export class FileEncryptionService {
  
  // Encrypt a file for upload
  static async encryptFileForUpload(file: File): Promise<EncryptedFileData> {
    if (!keyManager.isInitialized()) {
      throw new Error('Key manager not initialized. Please log in first.')
    }

    try {
      // Read file data
      const fileData = await this.readFileAsArrayBuffer(file)
      
      // Calculate original checksum
      const originalChecksum = calculateChecksum(fileData)
      
      // Generate unique key for this file
      const fileKey = generateFileKey()
      
      // Encrypt the file data
      const encryptedData = await encryptFile(fileData, fileKey)
      
      // Encrypt the file key with user's master key
      const encryptedKey = keyManager.encryptFileKey(fileKey)
      
      // Calculate encrypted data checksum
      const encryptedChecksum = calculateChecksum(encryptedData)
      
      return {
        encryptedData,
        encryptedKey,
        checksum: originalChecksum,
        originalSize: fileData.byteLength,
        encryptedSize: encryptedData.byteLength
      }
    } catch (error) {
      console.error('File encryption failed:', error)
      throw new Error('Failed to encrypt file')
    }
  }

  // Decrypt a file after download
  static async decryptFileAfterDownload(
    encryptedData: ArrayBuffer,
    encryptedKey: string,
    originalChecksum: string
  ): Promise<FileDecryptionResult> {
    if (!keyManager.isInitialized()) {
      throw new Error('Key manager not initialized. Please log in first.')
    }

    try {
      // Decrypt the file key
      const fileKey = keyManager.decryptFileKey(encryptedKey)
      
      // Decrypt the file data
      const decryptedData = await decryptFile(encryptedData, fileKey)
      
      // Verify integrity
      const calculatedChecksum = calculateChecksum(decryptedData)
      const isValid = calculatedChecksum === originalChecksum
      
      if (!isValid) {
        console.warn('File integrity check failed')
      }
      
      return {
        decryptedData,
        isValid,
        originalChecksum
      }
    } catch (error) {
      console.error('File decryption failed:', error)
      throw new Error('Failed to decrypt file')
    }
  }

  // Encrypt file data in chunks for large files
  static async encryptFileInChunks(
    file: File,
    chunkSize: number = 1024 * 1024, // 1MB chunks
    onProgress?: (progress: number) => void
  ): Promise<EncryptedFileData> {
    if (!keyManager.isInitialized()) {
      throw new Error('Key manager not initialized. Please log in first.')
    }

    try {
      const fileKey = generateFileKey()
      const encryptedChunks: ArrayBuffer[] = []
      let totalProcessed = 0
      
      // Read and encrypt file in chunks
      for (let offset = 0; offset < file.size; offset += chunkSize) {
        const chunk = file.slice(offset, offset + chunkSize)
        const chunkData = await this.readFileAsArrayBuffer(chunk)
        const encryptedChunk = await encryptFile(chunkData, fileKey)
        
        encryptedChunks.push(encryptedChunk)
        totalProcessed += chunkData.byteLength
        
        if (onProgress) {
          onProgress((totalProcessed / file.size) * 100)
        }
      }
      
      // Combine encrypted chunks
      const totalSize = encryptedChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0)
      const combinedData = new ArrayBuffer(totalSize)
      const combinedView = new Uint8Array(combinedData)
      
      let offset = 0
      for (const chunk of encryptedChunks) {
        combinedView.set(new Uint8Array(chunk), offset)
        offset += chunk.byteLength
      }
      
      // Calculate checksums
      const originalData = await this.readFileAsArrayBuffer(file)
      const originalChecksum = calculateChecksum(originalData)
      
      // Encrypt file key
      const encryptedKey = keyManager.encryptFileKey(fileKey)
      
      return {
        encryptedData: combinedData,
        encryptedKey,
        checksum: originalChecksum,
        originalSize: file.size,
        encryptedSize: combinedData.byteLength
      }
    } catch (error) {
      console.error('Chunked file encryption failed:', error)
      throw new Error('Failed to encrypt file in chunks')
    }
  }

  // Decrypt file data in chunks for large files
  static async decryptFileInChunks(
    encryptedData: ArrayBuffer,
    encryptedKey: string,
    originalChecksum: string,
    chunkSize: number = 1024 * 1024, // 1MB chunks
    onProgress?: (progress: number) => void
  ): Promise<FileDecryptionResult> {
    if (!keyManager.isInitialized()) {
      throw new Error('Key manager not initialized. Please log in first.')
    }

    try {
      const fileKey = keyManager.decryptFileKey(encryptedKey)
      const decryptedChunks: ArrayBuffer[] = []
      let totalProcessed = 0
      
      // Decrypt data in chunks
      for (let offset = 0; offset < encryptedData.byteLength; offset += chunkSize) {
        const chunkEnd = Math.min(offset + chunkSize, encryptedData.byteLength)
        const chunk = encryptedData.slice(offset, chunkEnd)
        const decryptedChunk = await decryptFile(chunk, fileKey)
        
        decryptedChunks.push(decryptedChunk)
        totalProcessed += chunk.byteLength
        
        if (onProgress) {
          onProgress((totalProcessed / encryptedData.byteLength) * 100)
        }
      }
      
      // Combine decrypted chunks
      const totalSize = decryptedChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0)
      const combinedData = new ArrayBuffer(totalSize)
      const combinedView = new Uint8Array(combinedData)
      
      let offset = 0
      for (const chunk of decryptedChunks) {
        combinedView.set(new Uint8Array(chunk), offset)
        offset += chunk.byteLength
      }
      
      // Verify integrity
      const calculatedChecksum = calculateChecksum(combinedData)
      const isValid = calculatedChecksum === originalChecksum
      
      return {
        decryptedData: combinedData,
        isValid,
        originalChecksum
      }
    } catch (error) {
      console.error('Chunked file decryption failed:', error)
      throw new Error('Failed to decrypt file in chunks')
    }
  }

  // Helper method to read file as ArrayBuffer
  private static readFileAsArrayBuffer(file: File | Blob): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as ArrayBuffer)
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsArrayBuffer(file)
    })
  }

  // Create downloadable blob from decrypted data
  static createDownloadableBlob(
    decryptedData: ArrayBuffer,
    mimeType: string = 'application/octet-stream'
  ): Blob {
    return new Blob([decryptedData], { type: mimeType })
  }

  // Trigger file download
  static downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}
