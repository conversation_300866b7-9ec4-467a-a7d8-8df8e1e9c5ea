'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent } from '@/components/ui/Card'
import { FileList } from '@/components/FileList'
import { FileUpload } from '@/components/FileUpload'
import { Breadcrumbs } from '@/components/Breadcrumbs'
import { 
  Upload, 
  FolderPlus, 
  Search, 
  Filter,
  Grid,
  List,
  RefreshCw
} from 'lucide-react'
import { FileRecord } from '@/lib/supabase'
import { debounce } from '@/lib/utils'
import toast from 'react-hot-toast'

interface FileExplorerState {
  files: FileRecord[]
  breadcrumbs: Array<{ id: string | null; name: string }>
  currentFolderId: string | null
  loading: boolean
  searchQuery: string
  viewMode: 'grid' | 'list'
  sortBy: 'name' | 'size' | 'created_at' | 'updated_at'
  sortOrder: 'asc' | 'desc'
  selectedFiles: Set<string>
}

export function FileExplorer() {
  const [state, setState] = useState<FileExplorerState>({
    files: [],
    breadcrumbs: [{ id: null, name: 'Home' }],
    currentFolderId: null,
    loading: true,
    searchQuery: '',
    viewMode: 'list',
    sortBy: 'name',
    sortOrder: 'asc',
    selectedFiles: new Set()
  })

  const [showUpload, setShowUpload] = useState(false)
  const [showCreateFolder, setShowCreateFolder] = useState(false)
  const [newFolderName, setNewFolderName] = useState('')

  // Debounced search function
  const debouncedSearch = debounce((query: string) => {
    loadFiles(state.currentFolderId, query)
  }, 300)

  // Load files from API
  const loadFiles = async (folderId: string | null = null, search: string = '') => {
    setState(prev => ({ ...prev, loading: true }))

    try {
      const params = new URLSearchParams({
        ...(folderId && { parentId: folderId }),
        ...(search && { search }),
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        limit: '100'
      })

      const response = await fetch(`/api/files?${params}`)
      const result = await response.json()

      if (result.success) {
        setState(prev => ({
          ...prev,
          files: result.data.files,
          breadcrumbs: result.data.breadcrumbs,
          loading: false
        }))
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('Failed to load files:', error)
      toast.error('Failed to load files')
      setState(prev => ({ ...prev, loading: false }))
    }
  }

  // Navigate to folder
  const navigateToFolder = (folderId: string | null) => {
    setState(prev => ({ 
      ...prev, 
      currentFolderId: folderId,
      selectedFiles: new Set()
    }))
    loadFiles(folderId, state.searchQuery)
  }

  // Create new folder
  const createFolder = async () => {
    if (!newFolderName.trim()) return

    try {
      const path = state.currentFolderId 
        ? `${state.breadcrumbs.slice(1).map(b => b.name).join('/')}/${newFolderName}`
        : newFolderName

      const response = await fetch('/api/files', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newFolderName,
          parentId: state.currentFolderId,
          path: `/${path}`
        })
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Folder created successfully')
        setNewFolderName('')
        setShowCreateFolder(false)
        loadFiles(state.currentFolderId, state.searchQuery)
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('Failed to create folder:', error)
      toast.error('Failed to create folder')
    }
  }

  // Handle search
  const handleSearch = (query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }))
    debouncedSearch(query)
  }

  // Handle sorting
  const handleSort = (sortBy: string) => {
    const newSortOrder = state.sortBy === sortBy && state.sortOrder === 'asc' ? 'desc' : 'asc'
    setState(prev => ({ ...prev, sortBy: sortBy as any, sortOrder: newSortOrder }))
    loadFiles(state.currentFolderId, state.searchQuery)
  }

  // Handle file selection
  const toggleFileSelection = (fileId: string) => {
    setState(prev => {
      const newSelected = new Set(prev.selectedFiles)
      if (newSelected.has(fileId)) {
        newSelected.delete(fileId)
      } else {
        newSelected.add(fileId)
      }
      return { ...prev, selectedFiles: newSelected }
    })
  }

  // Select all files
  const selectAllFiles = () => {
    setState(prev => ({
      ...prev,
      selectedFiles: new Set(prev.files.map(f => f.id))
    }))
  }

  // Clear selection
  const clearSelection = () => {
    setState(prev => ({ ...prev, selectedFiles: new Set() }))
  }

  // Initial load
  useEffect(() => {
    loadFiles()
  }, [])

  // Update files when sort changes
  useEffect(() => {
    if (!state.loading) {
      loadFiles(state.currentFolderId, state.searchQuery)
    }
  }, [state.sortBy, state.sortOrder])

  return (
    <div className="space-y-6">
      {/* Toolbar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-wrap gap-2">
              <Button onClick={() => setShowUpload(true)}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Files
              </Button>
              <Button variant="outline" onClick={() => setShowCreateFolder(true)}>
                <FolderPlus className="h-4 w-4 mr-2" />
                New Folder
              </Button>
              <Button variant="outline" onClick={() => loadFiles(state.currentFolderId, state.searchQuery)}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>

            <div className="flex gap-2 items-center">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search files..."
                  value={state.searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              
              <div className="flex border rounded-md">
                <Button
                  variant={state.viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setState(prev => ({ ...prev, viewMode: 'list' }))}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={state.viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setState(prev => ({ ...prev, viewMode: 'grid' }))}
                >
                  <Grid className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Selection toolbar */}
          {state.selectedFiles.size > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm text-blue-800">
                {state.selectedFiles.size} file(s) selected
              </span>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={selectAllFiles}>
                  Select All
                </Button>
                <Button size="sm" variant="outline" onClick={clearSelection}>
                  Clear Selection
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Breadcrumbs */}
      <Breadcrumbs 
        breadcrumbs={state.breadcrumbs}
        onNavigate={navigateToFolder}
      />

      {/* File List */}
      <FileList
        files={state.files}
        loading={state.loading}
        viewMode={state.viewMode}
        sortBy={state.sortBy}
        sortOrder={state.sortOrder}
        selectedFiles={state.selectedFiles}
        onSort={handleSort}
        onNavigate={navigateToFolder}
        onSelect={toggleFileSelection}
        onRefresh={() => loadFiles(state.currentFolderId, state.searchQuery)}
      />

      {/* Upload Modal */}
      {showUpload && (
        <FileUpload
          currentFolderId={state.currentFolderId}
          currentPath={state.breadcrumbs.slice(1).map(b => b.name).join('/')}
          onClose={() => setShowUpload(false)}
          onSuccess={() => {
            setShowUpload(false)
            loadFiles(state.currentFolderId, state.searchQuery)
          }}
        />
      )}

      {/* Create Folder Modal */}
      {showCreateFolder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Create New Folder</h3>
              <div className="space-y-4">
                <Input
                  placeholder="Folder name"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && createFolder()}
                />
                <div className="flex gap-2 justify-end">
                  <Button variant="outline" onClick={() => setShowCreateFolder(false)}>
                    Cancel
                  </Button>
                  <Button onClick={createFolder} disabled={!newFolderName.trim()}>
                    Create
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
